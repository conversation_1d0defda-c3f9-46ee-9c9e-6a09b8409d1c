import React from "react";
import { <PERSON><PERSON>er<PERSON>outer, Routes, Route, Link, useNavigate, useParams, Navigate } from "react-router-dom";
import {
  ThemeProvider,
  createTheme,
  CssBaseline,
  AppBar,
  Toolbar,
  Container,
  Box,
  Typography,
  Button,
  IconButton,
  Card,
  CardContent,
  CardActions,
  TextField,
  Grid,
  Paper,
  Chip,
  Stack,
  Divider,
  Menu,
  MenuItem,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Breadcrumbs,
  Tabs,
  Tab,
  Alert,
  SvgIcon,
  SvgIconOwnProps
} from "@mui/material";
import { DataGrid } from "@mui/x-data-grid";
import { CommonProps } from "@mui/material/OverridableComponent";
import { JSX } from "react/jsx-runtime";

/****************************
 * Inline SVG icons (avoid @mui/icons-material CDN fetch)
 ****************************/
const HomeIcon = (props: JSX.IntrinsicAttributes & { component: React.ElementType<any, keyof React.JSX.IntrinsicElements>; } & SvgIconOwnProps & CommonProps & Omit<any, "children" | "className" | "style" | "classes" | "color" | "sx" | "fontSize" | "shapeRendering" | "htmlColor" | "inheritViewBox" | "titleAccess" | "viewBox">) => (
  <SvgIcon {...props}>
    <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z" />
  </SvgIcon>
);
const LoginIcon = (props: JSX.IntrinsicAttributes & { component: React.ElementType<any, keyof React.JSX.IntrinsicElements>; } & SvgIconOwnProps & CommonProps & Omit<any, "style" | "children" | "className" | "classes" | "color" | "sx" | "fontSize" | "shapeRendering" | "htmlColor" | "inheritViewBox" | "titleAccess" | "viewBox">) => (
  <SvgIcon {...props}>
    <path d="M10 17l1.41-1.41L9.83 14H20v-2H9.83l1.58-1.59L10 9l-4 4 4 4z" />
    <path d="M4 5h8v2H6v10h6v2H4z" />
  </SvgIcon>
);
const AppRegistrationIcon = (props: JSX.IntrinsicAttributes & { component: React.ElementType<any, keyof React.JSX.IntrinsicElements>; } & SvgIconOwnProps & CommonProps & Omit<any, "style" | "children" | "className" | "classes" | "color" | "sx" | "fontSize" | "shapeRendering" | "htmlColor" | "inheritViewBox" | "titleAccess" | "viewBox">) => (
  <SvgIcon {...props}>
    <path d="M5 3h10a2 2 0 012 2v6h-2V5H5v14h10v-6h2v6a2 2 0 01-2 2H5a2 2 0 01-2-2V5a2 2 0 012-2z" />
    <path d="M21.5 11.5l1.5 1.5-6.5 6.5-3-3 1.5-1.5 1.5 1.5 4-4z" />
  </SvgIcon>
);
const LogoutIcon = (props: JSX.IntrinsicAttributes & { component: React.ElementType<any, keyof React.JSX.IntrinsicElements>; } & SvgIconOwnProps & CommonProps & Omit<any, "style" | "children" | "className" | "classes" | "color" | "sx" | "fontSize" | "shapeRendering" | "htmlColor" | "inheritViewBox" | "titleAccess" | "viewBox">) => (
  <SvgIcon {...props}>
    <path d="M10 17l1.41-1.41L9.83 14H20v-2H9.83l1.58-1.59L10 9l-4 4 4 4z" />
    <path d="M4 5h8v2H6v10h6v2H4z" />
  </SvgIcon>
);
const AssignmentIcon = (props: JSX.IntrinsicAttributes & { component: React.ElementType<any, keyof React.JSX.IntrinsicElements>; } & SvgIconOwnProps & CommonProps & Omit<any, "style" | "children" | "className" | "classes" | "color" | "sx" | "fontSize" | "shapeRendering" | "htmlColor" | "inheritViewBox" | "titleAccess" | "viewBox">) => (
  <SvgIcon {...props}>
    <path d="M19 3h-4.18C14.4 1.84 13.3 1 12 1s-2.4.84-2.82 2H5a2 2 0 00-2 2v14a2 2 0 002 2h14a2 2 0 002-2V5a2 2 0 00-2-2zm-7-1c.55 0 1 .45 1 1h-2c0-.55.45-1 1-1zM5 21V5h14v16H5z" />
  </SvgIcon>
);
const AssignmentTurnedInIcon = (props: JSX.IntrinsicAttributes & { component: React.ElementType<any, keyof React.JSX.IntrinsicElements>; } & SvgIconOwnProps & CommonProps & Omit<any, "style" | "children" | "className" | "classes" | "color" | "sx" | "fontSize" | "shapeRendering" | "htmlColor" | "inheritViewBox" | "titleAccess" | "viewBox">) => (
  <SvgIcon {...props}>
    <path d="M19 3h-4.18C14.4 1.84 13.3 1 12 1s-2.4.84-2.82 2H5a2 2 0 00-2 2v14a2 2 0 002 2h14a2 2 0 002-2V5a2 2 0 00-2-2zM5 21V5h14v16H5z" />
    <path d="M9.5 13.5l-2-2L6 13l3.5 3.5L18 8l-1.5-1.5z" />
  </SvgIcon>
);
const AdminIcon = (props: JSX.IntrinsicAttributes & { component: React.ElementType<any, keyof React.JSX.IntrinsicElements>; } & SvgIconOwnProps & CommonProps & Omit<any, "style" | "children" | "className" | "classes" | "color" | "sx" | "fontSize" | "shapeRendering" | "htmlColor" | "inheritViewBox" | "titleAccess" | "viewBox">) => (
  <SvgIcon {...props}>
    <path d="M12 2l7 3v6c0 5-3.5 9.74-7 11-3.5-1.26-7-6-7-11V5l7-3z" />
    <circle cx="12" cy="10" r="2.5" />
  </SvgIcon>
);
const PeopleIcon = (props: JSX.IntrinsicAttributes & { component: React.ElementType<any, keyof React.JSX.IntrinsicElements>; } & SvgIconOwnProps & CommonProps & Omit<any, "style" | "children" | "className" | "classes" | "color" | "sx" | "fontSize" | "shapeRendering" | "htmlColor" | "inheritViewBox" | "titleAccess" | "viewBox">) => (
  <SvgIcon {...props}>
    <circle cx="8" cy="10" r="3" />
    <circle cx="16" cy="10" r="3" />
    <path d="M2 20c0-3 3-5 6-5s6 2 6 5H2z" />
    <path d="M12 20c0-3 3-5 6-5s6 2 6 5h-6z" />
  </SvgIcon>
);
const PlaceIcon = (props: JSX.IntrinsicAttributes & { component: React.ElementType<any, keyof React.JSX.IntrinsicElements>; } & SvgIconOwnProps & CommonProps & Omit<any, "style" | "children" | "className" | "classes" | "color" | "sx" | "fontSize" | "shapeRendering" | "htmlColor" | "inheritViewBox" | "titleAccess" | "viewBox">) => (
  <SvgIcon {...props}>
    <path d="M12 2a6 6 0 00-6 6c0 4.5 6 12 6 12s6-7.5 6-12a6 6 0 00-6-6zm0 8a2 2 0 110-4 2 2 0 010 4z" />
  </SvgIcon>
);
const DescriptionIcon = (props: JSX.IntrinsicAttributes & { component: React.ElementType<any, keyof React.JSX.IntrinsicElements>; } & SvgIconOwnProps & CommonProps & Omit<any, "style" | "children" | "className" | "classes" | "color" | "sx" | "fontSize" | "shapeRendering" | "htmlColor" | "inheritViewBox" | "titleAccess" | "viewBox">) => (
  <SvgIcon {...props}>
    <path d="M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8l-6-6zM13 9V3.5L18.5 9H13zM8 13h8v2H8v-2zm0 4h8v2H8v-2z" />
  </SvgIcon>
);
const ArrowBackIcon = (props: JSX.IntrinsicAttributes & { component: React.ElementType<any, keyof React.JSX.IntrinsicElements>; } & SvgIconOwnProps & CommonProps & Omit<any, "style" | "children" | "className" | "classes" | "color" | "sx" | "fontSize" | "shapeRendering" | "htmlColor" | "inheritViewBox" | "titleAccess" | "viewBox">) => (
  <SvgIcon {...props}>
    <path d="M20 11H7.83l5.59-5.59L12 4 4 12l8 8 1.41-1.41L7.83 13H20v-2z" />
  </SvgIcon>
);
const CheckIcon = (props: JSX.IntrinsicAttributes & { component: React.ElementType<any, keyof React.JSX.IntrinsicElements>; } & SvgIconOwnProps & CommonProps & Omit<any, "style" | "children" | "className" | "classes" | "color" | "sx" | "fontSize" | "shapeRendering" | "htmlColor" | "inheritViewBox" | "titleAccess" | "viewBox">) => (
  <SvgIcon {...props}>
    <path d="M12 2a10 10 0 110 20 10 10 0 010-20zm-1 14l-4-4 1.41-1.41L11 12.17l5.59-5.59L18 8l-7 8z" />
  </SvgIcon>
);
const XIcon = (props: JSX.IntrinsicAttributes & { component: React.ElementType<any, keyof React.JSX.IntrinsicElements>; } & SvgIconOwnProps & CommonProps & Omit<any, "style" | "children" | "className" | "classes" | "color" | "sx" | "fontSize" | "shapeRendering" | "htmlColor" | "inheritViewBox" | "titleAccess" | "viewBox">) => (
  <SvgIcon {...props}>
    <path d="M12 2a10 10 0 110 20 10 10 0 010-20zm3.59 5L12 10.59 8.41 7 7 8.41 10.59 12 7 15.59 8.41 17 12 13.41 15.59 17 17 15.59 13.41 12 17 8.41 15.59 7z" />
  </SvgIcon>
);

/****************************
 * Theme — GBA-inspired palette
 * (Approximate from site visuals: deep navy, warm gold, off‑white)
 ****************************/
export const paletteTokens = {
  // Sampled from gbfamilylaw.com (brand navy/gold/off‑white family)
  // If you have the official tokens, share them and I'll lock them in exactly.
  navy: "#0B2D3C",
  navyDark: "#081F2A",
  gold: "#C6A15B",
  goldDark: "#A9853E",
  offWhite: "#F6F3EE",
  slate: "#33424F"
};

const theme = createTheme({
  palette: {
    mode: "light",
    primary: { main: paletteTokens.navy, contrastText: "#FFFFFF" },
    secondary: { main: paletteTokens.gold, contrastText: "#0B2D3C" },
    background: { default: paletteTokens.offWhite, paper: "#FFFFFF" },
    text: { primary: paletteTokens.slate }
  },
  shape: { borderRadius: 14 },
  typography: {
    fontFamily: ["Inter", "Segoe UI", "Roboto", "Helvetica", "Arial", "sans-serif"].join(","),
    h1: { color: paletteTokens.navy },
    h2: { color: paletteTokens.navy },
    h3: { color: paletteTokens.navy },
    h4: { color: paletteTokens.navy }
  },
  components: {
    MuiAppBar: { styleOverrides: { root: { boxShadow: "0 4px 14px rgba(0,0,0,0.08)", borderBottom: "none" } } },
    MuiButton: { styleOverrides: { root: { borderRadius: 12, textTransform: "none", fontWeight: 600 } } },
    MuiCard: { styleOverrides: { root: { borderRadius: 18, boxShadow: "0 8px 24px rgba(11,45,60,0.06)" } } },
    MuiPaper: { styleOverrides: { root: { borderRadius: 16 } } },
    MuiTabs: { styleOverrides: { indicator: { backgroundColor: paletteTokens.gold } } },
    MuiChip: { styleOverrides: { colorSecondary: { backgroundColor: paletteTokens.gold, color: paletteTokens.navy } } }
  }
});

/****************************
 * Fake auth & data layer (replace with real API)
 ****************************/
const DB = {
  users: [
    { id: 1, name: "Alex Morgan", email: "<EMAIL>", role: "admin" },
    { id: 2, name: "Jamie Rivera", email: "<EMAIL>", role: "user" }
  ],
  locations: [
    { id: 1, name: "Dallas", state: "TX", email: "<EMAIL>", phone: "************" },
    { id: 2, name: "Austin", state: "TX", email: "<EMAIL>", phone: "************" }
  ],
  forms: [
    { id: 101, title: "New Client Intake", status: "available" },
    { id: 102, title: "Financial Disclosure", status: "available" },
    { id: 103, title: "Child Custody Questionnaire", status: "available" }
  ],
  assignments: [
    // userId → formId → status
    { id: 1, userId: 2, formId: 101, status: "in_progress" },
    { id: 2, userId: 2, formId: 102, status: "completed" }
  ]
};

interface User {
  id: number;
  name: string;
  email: string;
  role: string;
}

interface AuthContextType {
  user: User | null;
  login: (email: string) => void;
  logout: () => void;
}

const AuthContext = React.createContext<AuthContextType | null>(null);
function useAuth() {
  const context = React.useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = React.useState<User | null>(null);
  const login = (email: string) => {
    const u = DB.users.find((x) => x.email === email) || DB.users[1];
    setUser(u);
  };
  const logout = () => setUser(null);
  return <AuthContext.Provider value={{ user, login, logout }}>{children}</AuthContext.Provider>;
}

function ProtectedRoute({ roles, children }: { roles?: string[]; children: React.ReactNode }) {
  const { user } = useAuth();
  if (!user) return <Navigate to="/login" replace />;
  if (roles && !roles.includes(user.role)) return <Navigate to="/" replace />;
  return children;
}

/****************************
 * Layout
 ****************************/
function Shell({ children }: { children: React.ReactNode }) {
  const { user, logout } = useAuth();
  const [anchor, setAnchor] = React.useState<HTMLElement | null>(null);
  const open = Boolean(anchor);
  const handleMenu = (e: React.MouseEvent<HTMLElement>) => setAnchor(e.currentTarget);
  const closeMenu = () => setAnchor(null);

  return (
    <>
      <AppBar position="sticky" color="primary" enableColorOnDark sx={{ bgcolor: 'primary.main' }}>
        <Toolbar sx={{ minHeight: 72, color: 'primary.contrastText' }}>
          <Typography component={Link} to="/" variant="h6" sx={{ flexGrow: 1, color: 'primary.contrastText', textDecoration: 'none', fontWeight: 800 }}>
            GBA Intake Portal
          </Typography>
          <Stack direction="row" spacing={1}>
            <Button component={Link as any} to="/" startIcon={<HomeIcon component={"symbol"} />} color="inherit">Home</Button>
            {!user && (
              <>
                <Button component={Link as any} to="/register" startIcon={<AppRegistrationIcon component={"symbol"} />} color="inherit">Register</Button>
                <Button component={Link as any} to="/login" startIcon={<LoginIcon component={"symbol"} />} variant="contained" color="secondary">Login</Button>
              </>
            )}
            {user && (
              <>
                <Button component={Link as any} to="/user" startIcon={<AssignmentIcon component={"symbol"} />} color="inherit">My Forms</Button>
                {user.role === "admin" && (
                  <Button component={Link as any} to="/admin" startIcon={<AdminIcon component={"symbol"} />} color="secondary" variant="contained">Admin</Button>
                )}
                <IconButton onClick={handleMenu}><Avatar sx={{ bgcolor: 'secondary.main', color: 'secondary.contrastText' }}>{user.name[0]}</Avatar></IconButton>
                <Menu anchorEl={anchor} open={open} onClose={closeMenu} keepMounted>
                  <MenuItem disabled>{user.name}</MenuItem>
                  <MenuItem onClick={() => { closeMenu(); logout(); }}>
                    <ListItemIcon><LogoutIcon fontSize="small" component={"symbol"} /></ListItemIcon>
                    Logout
                  </MenuItem>
                </Menu>
              </>
            )}
          </Stack>
        </Toolbar>
      </AppBar>
      <Container maxWidth="lg" sx={{ py: 4 }}>{children}</Container>
      <Box component="footer" sx={{ mt: 6, py: 4, textAlign: "center", bgcolor: paletteTokens.navy, color: "#fff" }}>
        <Typography variant="body2">© {new Date().getFullYear()} Client Intake Portal</Typography>
      </Box>
    </>
  );
}

/****************************
 * Screens
 ****************************/
function Home() {
  return (
    <Box>
      <Paper sx={{ p: { xs: 4, md: 6 }, background: `linear-gradient(135deg, ${paletteTokens.offWhite} 0%, #fff 100%)`, mb: 4, border: `1px solid rgba(11,45,60,0.08)` }}>
        <Grid container spacing={4} alignItems="center">
          <Grid size={{ xs: 12, md: 7 }}>
            <Typography variant="h3" fontWeight={800} color="primary.main" gutterBottom>
              Welcome to the Client Intake Portal
            </Typography>
            <Typography variant="h6" color="text.secondary" gutterBottom>
              Securely register, log in, and complete your intake forms online.
            </Typography>
            <Stack direction="row" spacing={2} sx={{ mt: 2 }}>
              <Button component={Link as any} to="/register" size="large" variant="contained" color="secondary">Create Account</Button>
              <Button component={Link as any} to="/login" size="large" variant="outlined">I already have an account</Button>
            </Stack>
          </Grid>
          <Grid size={{ xs: 12, md: 5 }}>
            <Box sx={{ p: 3, borderRadius: 4, bgcolor: "#fff", border: `1px solid rgba(0,0,0,0.06)` }}>
              <Stack spacing={2}>
                <Stack direction="row" spacing={1} alignItems="center">
                  <AssignmentIcon component={"symbol"} />
                  <Typography fontWeight={700}>Fill forms online</Typography>
                </Stack>
                <Stack direction="row" spacing={1} alignItems="center">
                  <AssignmentTurnedInIcon component={"symbol"} />
                  <Typography fontWeight={700}>Save progress anytime</Typography>
                </Stack>
                <Stack direction="row" spacing={1} alignItems="center">
                  <AdminIcon component={"symbol"} />
                  <Typography fontWeight={700}>Admins manage users & locations</Typography>
                </Stack>
              </Stack>
            </Box>
          </Grid>
        </Grid>
      </Paper>

      <Grid container spacing={3}>
        {["Register", "Login", "Start Forms"].map((t, i) => (
          <Grid key={t} size={{ xs: 12, md: 4 }}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>{t}</Typography>
                <Typography variant="body2" color="text.secondary">
                  {i === 0 && "Create your secure account to get started."}
                  {i === 1 && "Access your portal with your email and password."}
                  {i === 2 && "Choose from available forms and complete at your pace."}
                </Typography>
              </CardContent>
              <CardActions>
                <Button component={Link as any} to={i === 0 ? "/register" : i === 1 ? "/login" : "/user"} size="small">Go</Button>
              </CardActions>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Box>
  );
}

function Register() {
  const nav = useNavigate();
  return (
    <Grid container spacing={3}>
      <Grid size={{ xs: 12, md: 6 }}>
        <Paper sx={{ p: 4 }}>
          <Typography variant="h5" fontWeight={700} gutterBottom>Create your account</Typography>
          <Stack spacing={2}>
            <TextField label="Full name" fullWidth />
            <TextField label="Email" type="email" fullWidth />
            <TextField label="Password" type="password" fullWidth />
            <Button onClick={() => nav("/login")} variant="contained" color="primary">Register</Button>
            <Typography variant="body2">Already have an account? <Button component={Link as any} to="/login">Sign in</Button></Typography>
          </Stack>
        </Paper>
      </Grid>
    </Grid>
  );
}

function Login() {
  const { login } = useAuth();
  const nav = useNavigate();
  const [email, setEmail] = React.useState("");
  return (
    <Grid container spacing={3}>
      <Grid size={{ xs: 12, md: 6 }}>
        <Paper sx={{ p: 4 }}>
          <Typography variant="h5" fontWeight={700} gutterBottom>Sign in</Typography>
          <Stack spacing={2}>
            <TextField label="Email" type="email" value={email} onChange={(e) => setEmail(e.target.value)} fullWidth />
            <TextField label="Password" type="password" fullWidth />
            <Button onClick={() => { login(email); nav("/user"); }} variant="contained" startIcon={<LoginIcon component={"symbol"} />}>Login</Button>
          </Stack>
        </Paper>
      </Grid>
    </Grid>
  );
}

function UserHome() {
  const { user } = useAuth();
  const myAssignments = DB.assignments.filter(a => a.userId === user?.id);
  const available = DB.forms.filter(f => !myAssignments.find(a => a.formId === f.id));
  const inProgress = myAssignments.filter(a => a.status === "in_progress").map(a => DB.forms.find(f=>f.id===a.formId)).filter((f): f is Form => f !== undefined);
  const completed = myAssignments.filter(a => a.status === "completed").map(a => DB.forms.find(f=>f.id===a.formId)).filter((f): f is Form => f !== undefined);
  return (
    <Box>
      <Typography variant="h4" fontWeight={800} gutterBottom>My Forms</Typography>
      <Grid container spacing={3}>
        <FormBucket title="Available" items={available} color="primary" />
        <FormBucket title="In Progress" items={inProgress} color="secondary" />
        <FormBucket title="Completed" items={completed} color="success" />
      </Grid>
    </Box>
  );
}

interface Form {
  id: number;
  title: string;
  status: string;
}

function FormBucket({
  title,
  items,
  color
}: {
  title: string;
  items: Form[];
  color: "primary" | "secondary" | "success" | "error" | "info" | "warning";
}) {
  return (
    <Grid size={{ xs: 12, md: 4 }}>
      <Card>
        <CardContent>
          <Stack direction="row" spacing={1} alignItems="center" mb={1}>
            <AssignmentIcon color={color} component={"symbol"} />
            <Typography variant="h6">{title}</Typography>
            <Chip label={items.length} size="small" sx={{ ml: "auto" }} />
          </Stack>
          <List dense>
            {items.length === 0 && <Typography variant="body2" color="text.secondary">Nothing here yet.</Typography>}
            {items.map((f) => (
              <ListItem key={f.id} component={Link} to={`/forms/${f.id}`} sx={{ borderRadius: 2, mb: 0.5, '&:hover': { bgcolor: "action.hover" } }}>
                <ListItemIcon><DescriptionIcon component={"symbol"} /></ListItemIcon>
                <ListItemText primary={f.title} secondary={`Form #${f.id}`} />
              </ListItem>
            ))}
          </List>
        </CardContent>
      </Card>
    </Grid>
  );
}

function FormDetail() {
  const { id } = useParams();
  const form = DB.forms.find((f) => String(f.id) === id);
  const nav = useNavigate();
  return (
    <Box>
      <Stack direction="row" spacing={1} alignItems="center" mb={2}>
        <IconButton onClick={() => nav(-1)}><ArrowBackIcon component={"symbol"} /></IconButton>
        <Breadcrumbs>
          <Link to="/user">My Forms</Link>
          <Typography color="text.primary">{form?.title}</Typography>
        </Breadcrumbs>
      </Stack>

      <Grid container spacing={3}>
        <Grid size={{ xs: 12, md: 8 }}>
          <Card>
            <CardContent>
              <Typography variant="h5" fontWeight={700} gutterBottom>{form?.title}</Typography>
              <Stack spacing={2}>
                <TextField label="Full Legal Name" fullWidth />
                <Grid container spacing={2}>
                  <Grid size={{ xs: 12, md: 6 }}><TextField label="Email" fullWidth /></Grid>
                  <Grid size={{ xs: 12, md: 6 }}><TextField label="Phone" fullWidth /></Grid>
                </Grid>
                <TextField label="Street Address" fullWidth />
                <Grid container spacing={2}>
                  <Grid size={{ xs: 12, md: 6 }}><TextField label="City" fullWidth /></Grid>
                  <Grid size={{ xs: 6, md: 3 }}><TextField label="State" fullWidth /></Grid>
                  <Grid size={{ xs: 6, md: 3 }}><TextField label="ZIP" fullWidth /></Grid>
                </Grid>
                <TextField label="Brief case description" fullWidth multiline minRows={4} />
              </Stack>
            </CardContent>
            <CardActions sx={{ justifyContent: "space-between" }}>
              <Button variant="outlined">Save Draft</Button>
              <Button variant="contained" color="secondary" startIcon={<AssignmentTurnedInIcon component={"symbol"} />}>Submit</Button>
            </CardActions>
          </Card>
        </Grid>
        <Grid size={{ xs: 12, md: 4 }}>
          <Card sx={{ position: "sticky", top: 96 }}>
            <CardContent>
              <Typography variant="subtitle2" color="text.secondary">Progress</Typography>
              <Divider sx={{ my: 1 }} />
              <Stack spacing={1}>
                {[
                  "Contact Details",
                  "Address",
                  "Case Overview"
                ].map((s, i) => (
                  <Stack key={s} direction="row" alignItems="center" spacing={1}>
                    <Chip size="small" label={`${i + 1}`} color={i < 1 ? "secondary" : "default"} />
                    <Typography>{s}</Typography>
                  </Stack>
                ))}
              </Stack>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
}

/****************************
 * Admin Area
 ****************************/
function AdminHome() {
  const nav = useNavigate();
  return (
    <Box>
      <Typography variant="h4" fontWeight={800} gutterBottom>Admin</Typography>
      <Grid container spacing={3}>
        <AdminTile title="Users" icon={<PeopleIcon component={"symbol"} />} onClick={() => nav("/admin/users")} />
        <AdminTile title="Office Locations" icon={<PlaceIcon component={"symbol"} />} onClick={() => nav("/admin/locations")} />
        <AdminTile title="Forms" icon={<DescriptionIcon component={"symbol"} />} onClick={() => nav("/admin/forms")} />
      </Grid>
    </Box>
  );
}

function AdminTile({ title, icon, onClick }: { title: string; icon: React.ReactNode; onClick: () => void }) {
  return (
    <Grid size={{ xs: 12, md: 4 }}>
      <Paper onClick={onClick} sx={{ p: 3, cursor: "pointer", border: `1px solid rgba(0,0,0,0.06)`, '&:hover': { boxShadow: "0 8px 28px rgba(0,0,0,.08)", transform: "translateY(-2px)" }, transition: "all .2s ease" }}>
        <Stack direction="row" alignItems="center" spacing={2}>
          <Avatar sx={{ bgcolor: "primary.main" }}>{icon}</Avatar>
          <Typography variant="h6">{title}</Typography>
        </Stack>
      </Paper>
    </Grid>
  );
}

function AdminUsers() {
  const columns = [
    { field: "name", headerName: "Name", flex: 1 },
    { field: "email", headerName: "Email", flex: 1 },
    { field: "role", headerName: "Role", width: 120 },
    {
      field: "actions",
      headerName: "Actions",
      width: 220,
      sortable: false,
      renderCell: () => (
        <Stack direction="row" spacing={1}>
          <Button size="small" variant="outlined">Edit</Button>
          <Button size="small" color="error" variant="outlined">Delete</Button>
          <Button size="small" variant="contained" color="secondary">Assign Forms</Button>
        </Stack>
      )
    }
  ];
  return (
    <Box>
      <HeaderWithTabs title="Users" tabs={["All", "Admins", "Clients"]} />
      <Paper sx={{ height: 420, p: 1 }}>
        <DataGrid rows={DB.users} columns={[{ field: "id", headerName: "ID", width: 80 }, ...columns]} pageSizeOptions={[5, 10]} initialState={{ pagination: { paginationModel: { pageSize: 5 } } }} />
      </Paper>
    </Box>
  );
}

function AdminLocations() {
  const columns = [
    { field: "name", headerName: "Name", flex: 1 },
    { field: "state", headerName: "State", width: 120 },
    { field: "email", headerName: "Primary Email", flex: 1 },
    { field: "phone", headerName: "Primary Phone", width: 180 },
    {
      field: "actions",
      headerName: "Actions",
      width: 180,
      sortable: false,
      renderCell: () => (
        <Stack direction="row" spacing={1}>
          <Button size="small" variant="outlined">Edit</Button>
          <Button size="small" color="error" variant="outlined">Delete</Button>
        </Stack>
      )
    }
  ];
  return (
    <Box>
      <HeaderWithTabs title="Office Locations" tabs={["All", "Texas"]} />
      <Paper sx={{ height: 420, p: 1 }}>
        <DataGrid rows={DB.locations} columns={[{ field: "id", headerName: "ID", width: 80 }, ...columns]} pageSizeOptions={[5, 10]} initialState={{ pagination: { paginationModel: { pageSize: 5 } } }} />
      </Paper>
    </Box>
  );
}

function AdminForms() {
  const rows = DB.forms.map(f => ({ id: f.id, title: f.title, status: f.status }));
  const columns = [
    { field: "title", headerName: "Title", flex: 1 },
    { field: "status", headerName: "Status", width: 150 },
    {
      field: "actions",
      headerName: "Actions",
      width: 200,
      sortable: false,
      renderCell: () => (
        <Stack direction="row" spacing={1}>
          <Button size="small" variant="outlined">Edit</Button>
          <Button size="small" color="error" variant="outlined">Delete</Button>
        </Stack>
      )
    }
  ];
  return (
    <Box>
      <HeaderWithTabs title="Forms" tabs={["All", "Available", "Archived"]} />
      <Paper sx={{ height: 420, p: 1 }}>
        <DataGrid rows={rows} columns={[{ field: "id", headerName: "ID", width: 90 }, ...columns]} pageSizeOptions={[5, 10]} initialState={{ pagination: { paginationModel: { pageSize: 5 } } }} />
      </Paper>
    </Box>
  );
}

function HeaderWithTabs({ title, tabs }) {
  const [value, setValue] = React.useState(0);
  return (
    <Stack spacing={1} mb={2}>
      <Typography variant="h4" fontWeight={800}>{title}</Typography>
      <Tabs value={value} onChange={(e, v) => setValue(v)} sx={{ width: "100%", '& .MuiTabs-indicator': { backgroundColor: paletteTokens.gold } }}>
        {tabs.map((t) => <Tab key={t} label={t} />)}
      </Tabs>
    </Stack>
  );
}

/****************************
 * Tiny in-app test runner (smoke tests)
 ****************************/
function runSmokeTests() {
  const results = [];
  const expect = (name, pass, details = "") => results.push({ name, pass, details });

  try {
    expect("theme.primary.main matches paletteTokens.navy", theme.palette.primary.main === paletteTokens.navy, theme.palette.primary.main);
    const gradient = `linear-gradient(135deg, ${paletteTokens.offWhite} 0%, #fff 100%)`;
    expect("hero gradient contains offWhite token", gradient.includes(paletteTokens.offWhite));
    expect("DB has at least 1 admin", DB.users.some(u => u.role === "admin"));
    expect("DB has at least 1 user", DB.users.some(u => u.role === "user"));
    expect("Forms list non-empty", DB.forms.length > 0);
    // New tests
    expect("Custom icons are defined", [HomeIcon, LoginIcon, AssignmentIcon, DescriptionIcon].every(fn => typeof fn === 'function'));
    expect("Locations have required fields", DB.locations.every(l => l.name && l.state && l.email && l.phone));
  } catch (e) {
    results.push({ name: "Unhandled error in smoke tests", pass: false, details: String(e) });
  }
  return results;
}

function DevTests() {
  const results = React.useMemo(() => runSmokeTests(), []);
  return (
    <Box>
      <Typography variant="h4" fontWeight={800} gutterBottom>In‑App Smoke Tests</Typography>
      <Alert severity="info" sx={{ mb: 2 }}>These are lightweight checks to validate basic wiring. For full coverage, add unit/integration tests in your repo.</Alert>
      <Stack spacing={1}>
        {results.map((r, i) => (
          <Paper key={i} sx={{ p: 2, display: 'flex', alignItems: 'center', gap: 1 }}>
            {r.pass ? <CheckIcon color="success" /> : <XIcon color="error" />}
            <Typography fontWeight={600}>{r.name}</Typography>
            <Typography variant="body2" color="text.secondary" sx={{ ml: 'auto' }}>{String(r.details)}</Typography>
          </Paper>
        ))}
      </Stack>
    </Box>
  );
}

/****************************
 * App
 ****************************/
function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <AuthProvider>
        <BrowserRouter>
          <Routes>
            <Route path="/" element={<Shell><Home /></Shell>} />
            <Route path="/register" element={<Shell><Register /></Shell>} />
            <Route path="/login" element={<Shell><Login /></Shell>} />

            <Route path="/user" element={
              <ProtectedRoute roles={["user", "admin"]}>
                <Shell><UserHome /></Shell>
              </ProtectedRoute>
            } />

            <Route path="/forms/:id" element={
              <ProtectedRoute roles={["user", "admin"]}>
                <Shell><FormDetail /></Shell>
              </ProtectedRoute>
            } />

            <Route path="/admin" element={
              <ProtectedRoute roles={["admin"]}>
                <Shell><AdminHome /></Shell>
              </ProtectedRoute>
            } />
            <Route path="/admin/users" element={
              <ProtectedRoute roles={["admin"]}>
                <Shell><AdminUsers /></Shell>
              </ProtectedRoute>
            } />
            <Route path="/admin/locations" element={
              <ProtectedRoute roles={["admin"]}>
                <Shell><AdminLocations /></Shell>
              </ProtectedRoute>
            } />
            <Route path="/admin/forms" element={
              <ProtectedRoute roles={["admin"]}>
                <Shell><AdminForms /></Shell>
              </ProtectedRoute>
            } />

            {/* Dev-only test page */}
            <Route path="/_dev/tests" element={<Shell><DevTests /></Shell>} />
          </Routes>
        </BrowserRouter>
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App;
